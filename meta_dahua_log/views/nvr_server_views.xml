<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- NVR Server Tree View -->
    <record id="view_nvr_server_tree" model="ir.ui.view">
        <field name="name">nvr.server.tree</field>
        <field name="model">nvr.server</field>
        <field name="arch" type="xml">
            <tree string="NVR Servers">
                <field name="name"/>
                <field name="host"/>
                <field name="port"/>
                <field name="state" widget="statusbar" statusbar_visible="draft,tested,error"/>
                <field name="last_connection"/>
                <field name="log_count" widget="statinfo"/>
            </tree>
        </field>
    </record>

    <!-- NVR Server Form View -->
    <record id="view_nvr_server_form" model="ir.ui.view">
        <field name="name">nvr.server.form</field>
        <field name="model">nvr.server</field>
        <field name="arch" type="xml">
            <form string="NVR Server">
                <header>
                    <field name="state" widget="statusbar" statusbar_visible="draft,tested,error"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="test_connection" type="object" class="oe_stat_button" icon="fa-plug">
                            <field name="state" widget="statinfo" string="Test Connection"/>
                        </button>
                        <button name="fetch_logs" type="object" class="oe_stat_button" icon="fa-download">
                            <div>Fetch Logs</div>
                        </button>
                    </div>
                    <div class="oe_title">
                        <h1>
                            <field name="name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="active" widget="boolean_toggle" options="{'no_open': True}"/>
                            <field name="host"/>
                            <field name="port"/>
                            <field name="username"/>
                            <field name="password" password="True"/>
                            <field name="verify_ssl"/>
                        </group>
                        <group>
                            <field name="log_type_ids" widget="many2many_tags" options="{'no_create_edit': True}"/>
                            <field name="last_connection" readonly="1"/>
                            <field name="log_count" widget="statinfo" string="Logs" nolabel="1">
                                <tree string="Logs" create="false" delete="false">
                                    <field name="log_type"/>
                                    <field name="log_subtype"/>
                                    <field name="log_time"/>
                                    <field name="details"/>
                                </tree>
                            </field>
                        </group>
                    </group>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids" widget="mail_followers"/>
                    <field name="activity_ids" widget="mail_activity"/>
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Action to open NVR Server -->
    <record id="action_nvr_server" model="ir.actions.act_window">
        <field name="name">NVR Servers</field>
        <field name="res_model">nvr.server</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No NVR Server found. Let's create one!
            </p>
        </field>
    </record>

    <!-- Menu Item -->
    <menuitem id="menu_nvr_root" name="NVR" sequence="10"/>
    <menuitem id="menu_nvr_servers" name="NVR Servers" parent="menu_nvr_root" action="action_nvr_server" sequence="10"/>
</odoo>
