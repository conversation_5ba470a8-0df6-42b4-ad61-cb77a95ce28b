import requests
import json
import csv
import hashlib
import argparse
from datetime import datetime

class DahuaRpc:
    def __init__(self, host, username, password):
        self.host = host
        self.username = username
        self.password = password
        self.session = requests.Session()
        self.session_id = None
        self.id = 10

    def request(self, method, params=None, url=None):
        self.id += 1
        data = {"method": method, "id": self.id}
        if params:
            data["params"] = params
        if self.session_id:
            data["session"] = self.session_id
        if not url:
            url = f"http://{self.host}/RPC2"
        response = self.session.post(url, json=data)
        return response.json()

    def login(self):
        url = f"http://{self.host}/RPC2_Login"
        method = "global.login"
        # Step 1: Get session, realm, and random
        params = {
            "userName": self.username,
            "password": "",
            "clientType": "Web3.0"
        }
        response = self.request(method, params, url)
        self.session_id = response["session"]
        realm = response["params"]["realm"]
        random = response["params"]["random"]

        # Step 2: Encrypt password
        pwd_phrase = f"{self.username}:{realm}:{self.password}"
        pwd_hash = hashlib.md5(pwd_phrase.encode("utf-8")).hexdigest().upper()
        pass_phrase = f"{self.username}:{random}:{pwd_hash}"
        pass_hash = hashlib.md5(pass_phrase.encode("utf-8")).hexdigest().upper()

        # Step 3: Perform real login
        params = {
            "userName": self.username,
            "password": pass_hash,
            "clientType": "Web3.0",
            "authorityType": "Default",
            "passwordType": "Default"
        }
        response = self.request(method, params, url)
        return response["result"]

    def start_log_find(self, start_time, end_time, types, exclude_types=None):
        method = "log.startFind"
        params = {
            "condition": {
                "Types": types,
                "StartTime": start_time,
                "EndTime": end_time,
                "Translate": True,
                "Order": "Descent",
            }
        }
        if exclude_types:
            params["condition"]["ExcludeTypes"] = exclude_types
        response = self.request(method, params)
        if response.get("result"):
            return response["params"]["token"]
        else:
            raise Exception("Failed to start log find: " + str(response))

    def get_log_count(self, token):
        method = "log.getCount"
        params = {"token": token}
        response = self.request(method, params)
        if response.get("result"):
            return response["params"]["count"]
        else:
            raise Exception("Failed to get log count: " + str(response))

    def get_log_items(self, token, offset, count):
        method = "log.doSeekFind"
        params = {"token": token, "offset": offset, "count": count}
        response = self.request(method, params)
        if response.get("result"):
            return response["params"]["items"]
        else:
            raise Exception("Failed to get log items: " + str(response))

    def stop_log_find(self, token):
        method = "log.stopFind"
        params = {"token": token}
        response = self.request(method, params)
        return response["result"]

def main():
    parser = argparse.ArgumentParser(description="Fetch Dahua NVR logs and save to CSV")
    parser.add_argument("--host", required=True, help="NVR IP address (e.g., **************)")
    parser.add_argument("--username", required=True, help="Username for NVR login")
    parser.add_argument("--password", required=True, help="Password for NVR login")
    parser.add_argument("--start", required=True, help="Start time (YYYY-MM-DD HH:MM:SS)")
    parser.add_argument("--end", required=True, help="End time (YYYY-MM-DD HH:MM:SS)")
    parser.add_argument("--log-type", choices=["system", "event"], required=True, help="Type of logs to fetch: 'system' or 'event'")
    args = parser.parse_args()

    # Validate time format
    try:
        datetime.strptime(args.start, "%Y-%m-%d %H:%M:%S")
        datetime.strptime(args.end, "%Y-%m-%d %H:%M:%S")
    except ValueError:
        print("Error: Time format must be 'YYYY-MM-DD HH:MM:SS'")
        return

    dahua = DahuaRpc(args.host, args.username, args.password)
    if not dahua.login():
        print("Login failed")
        return

    try:
        if args.log_type == "system":
            types = ["System", "Config", "Storage", "Data", "Account", "ClearLog", "File", "RemoteDevice"]
            storage_numbered_errors = [
                "Storage.Error.1", "Storage.Error.2", "Storage.Error.3", "Storage.Error.4",
                "Storage.Error.5", "Storage.Error.6", "Storage.Error.7"
            ]
            
            storage_ac_errors = [
                "Storage.Error.ACErrDev", "Storage.Error.ACErrHSM", "Storage.Error.ACErrTimeout",
                "Storage.Error.ACErrMedia", "Storage.Error.ACErrATABus", "Storage.Error.ACErrHostBus",
                "Storage.Error.ACErrSystem", "Storage.Error.ACErrInvalid", "Storage.Error.ACErrOther"
            ]
            
            storage_fs_errors = [
                "Storage.Error.FSErrIndex", "Storage.Error.FSErrSuperBlock",
                "Storage.Error.FSErrPartition"
            ]
            
            storage_event_errors = [
                "Event.StorageNotExist.Start", "Event.StorageLowSpace.Start",
                "Event.InsufficientQuotaSpace.Start", "Event.StorageOverload.Start",
                "Event.StorageHealthAbnormal.Start", "Event.StorageHealthAlarm.Start",
                "Event.StorageHealthAlarm.Pulse"
            ]
            
            exclude_types = (storage_numbered_errors + storage_ac_errors + 
                           storage_fs_errors + storage_event_errors)
        elif args.log_type == "event":
            basic_events = [
                "Event", "Event.AlarmLocal.Start", "Event.AlarmLocal.Stop", "Event.AlarmNet.Start",
                "Event.AlarmNet.Stop"
            ]
            
            gb35114_events = [
                "GB35114.GenerateKeyFail", "GB35114.DataEncryptionFail", "GB35114.DataDecryptionFail",
                "GB35114.CryptoModuleAbnormal", "GB35114.CheckIntegrityFail", "GB35114.SignDataFail",
                "GB35114.CertNotFound", "GB35114.DigestDataFail", "GB35114.CheckSignatureFail"
            ]
            
            ipc_events = [
                "Event.IPCExtAlarm.Start", "Event.IPCExtAlarm.Stop", "Event.IPCOffline.Start",
                "Event.IPCOffline.Stop"
            ]
            
            video_events = [
                "Event.VideoMotion.Start", "Event.VideoMotion.Stop", "Event.VideoLoss.Start",
                "Event.VideoLoss.Stop", "Event.VideoBlind.Start", "Event.VideoBlind.Stop",
                "Event.SceneChange.Start", "Event.SceneChange.Stop"
            ]
            
            sensor_events = [
                "Event.AlarmPIR.Start", "Event.AlarmPIR.Stop", "Event.AudioDetect.Start",
                "Event.AudioDetect.Stop", "Event.HeatImaging.Start", "Event.HeatImaging.Stop"
            ]
            
            network_events = [
                "Event.NetAbort.Start", "Event.NetAbort.Stop", "Event.MacConflict.Start",
                "Event.MacConflict.Stop", "Event.UserLock"
            ]
            
            hardware_events = [
                "Event.TemperatureAlarm", "Event.FanSpeed.Start", "Event.FanSpeed.Stop"
            ]
            
            storage_events = [
                "Storage.Error.1", "Storage.Error.2", "Storage.Error.3", "Storage.Error.4",
                "Storage.Error.5", "Storage.Error.6", "Storage.Error.7", "Event.StorageNotExist.Start",
                "Event.StorageLowSpace.Start", "Event.StorageLowSpace.Stop"
            ]
            
            system_events = [
                "Event.IPConflict.Start Bimbo", "Event.IPConflict.Stop", "Event.SafetyAbnormal",
                "Event.BatteryLowPower.Start", "Event.BatteryLowPower.Stop"
            ]
            
            storage_health_events = [
                "Event.StorageHealthAbnormal.Start", "Event.StorageHealthAbnormal.Stop",
                "Event.InsufficientQuotaSpace.Start", "Event.StorageOverload.Start"
            ]
            
            storage_error_events = [
                "Storage.Error.ACErrDev", "Storage.Error.ACErrHSM", "Storage.Error.ACErrTimeout",
                "Storage.Error.ACErrMedia", "Storage.Error.ACErrATABus", "Storage.Error.ACErrHostBus",
                "Storage.Error.ACErrSystem", "Storage.Error.ACErrInvalid", "Storage.Error.ACErrOther",
                "Storage.Error.FSErrIndex", "Storage.Error.FSErrSuperBlock", "Storage.Error.FSErrPartition"
            ]
            
            power_events = [
                "Event.PowerFault.Start", "Event.PowerFault.Stop"
            ]
            
            misc_events = [
                "Event.ExtAlarm.Start", "Event.ExtAlarm.Stop", "Event.Intelligence.Start",
                "Event.Intelligence.Stop", "Event.Intelligence.Pluse", "Event.PassThrough",
                "Event.DialRecognition"
            ]
            
            types = (basic_events + gb35114_events + ipc_events + video_events + sensor_events +
                    network_events + hardware_events + storage_events + system_events +
                    storage_health_events + storage_error_events + power_events + misc_events)
            exclude_types = []

        token = dahua.start_log_find(args.start, args.end, types, exclude_types)
        count = dahua.get_log_count(token)
        print(f"Found {count} log entries")

        if count > 0:
            logs = []
            batch_size = 100
            for offset in range(0, count, batch_size):
                batch_count = min(batch_size, count - offset)
                batch_logs = dahua.get_log_items(token, offset, batch_count)
                logs.extend(batch_logs)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"nvr_logs_{timestamp}.csv"
            with open(filename, "w", newline="", encoding="utf-8") as f:
                writer = csv.writer(f)
                writer.writerow(["User", "Time", "Type", "Detail"])
                for log in logs:
                    detail = log.get("Detail", "")
                    detail = detail.replace('\n', ' ') if detail else ""
                    writer.writerow([log["User"], log["Time"], log["Type"], detail])
            print(f"Logs saved to {filename}")
        else:
            print("No logs found in the specified time range")
    except Exception as e:
        print(f"Error: {e}")
    finally:
        if 'token' in locals():
            dahua.stop_log_find(token)
            print("Log search stopped")

if __name__ == "__main__":
    main()


