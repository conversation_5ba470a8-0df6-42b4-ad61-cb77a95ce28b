# Dahua NVR Log Fetcher

A Python script to fetch logs from Dahua NVR devices and save them to CSV format.

## Requirements

- Python 3.6+
- Dahua NVR device with network access

## Installation

No special installation required. Just clone the repository and run the script.

## Usage

```bash
python dahua_log.py --host <nvr_ip> --username <user> --password <pass> --start "YYYY-MM-DD HH:MM:SS" --end "YYYY-MM-DD HH:MM:SS" --log-type <system|event>
```

### Arguments

- `--host`: NVR IP address (e.g., *************)
- `--username`: Username for NVR login
- `--password`: Password for NVR login
- `--start`: Start time in format "YYYY-MM-DD HH:MM:SS"
- `--end`: End time in format "YYYY-MM-DD HH:MM:SS"
- `--log-type`: Type of logs to fetch ('system' or 'event')

### Example

```bash
python dahua_log.py --host ************** --username admin --password '!?@#qwer' --start "2023-07-01 00:00:00" --end "2023-07-01 23:59:59" --log-type system
python dahua_log.py --host ************** --username admin --password '!?@#qwer' --start "2025-07-09 00:00:00" --end "2025-07-09 23:59:59" --log-type event
```

## Output

The script generates a CSV file with the following columns:
- User
- Time
- Type
- Detail

The filename includes a timestamp: `nvr_logs_YYYYMMDD_HHMMSS.csv`

