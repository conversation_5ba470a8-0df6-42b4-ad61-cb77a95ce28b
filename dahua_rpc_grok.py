import requests
import hashlib
import json

class DahuaRPC:
    def __init__(self, host, username, password):
        self.host = host
        self.username = username
        self.password = password
        self.session = requests.Session()
        self.session_id = None
        self.id = 10

    def request(self, method, params=None, url=None):
        self.id += 1
        data = {"method": method, "id": self.id}
        if params:
            data["params"] = params
        if self.session_id:
            data["session"] = self.session_id
        if not url:
            url = f"http://{self.host}/RPC2"
        response = self.session.post(url, json=data)
        return response.json()

    def login(self):
        url = f"http://{self.host}/RPC2_Login"
        method = "global.login"
        # Step 1: Get session, realm, and random
        params = {
            "userName": self.username,
            "password": "",
            "clientType": "Web3.0"
        }
        response = self.request(method, params, url)
        self.session_id = response["session"]
        realm = response["params"]["realm"]
        random = response["params"]["random"]
        # print(f"Get session, realm, and random: {response}")
        # print(f"Session ID: {self.session_id}, Realm: {realm}, Random: {random}")

        # return
        # Step 2: Encrypt password
        pwd_phrase = f"{self.username}:{realm}:{self.password}"
        pwd_hash = hashlib.md5(pwd_phrase.encode("utf-8")).hexdigest().upper()
        pass_phrase = f"{self.username}:{random}:{pwd_hash}"
        pass_hash = hashlib.md5(pass_phrase.encode("utf-8")).hexdigest().upper()

        # Step 3: Perform real login
        params = {
            "userName": self.username,
            "password": pass_hash,
            "clientType": "Web3.0",
            "authorityType": "Default",
            "passwordType": "Default"
        }
        response = self.request(method, params, url)
        # print(f"Login response: {response}")
        return response["result"]

    def current_time(self):
        """Get the current time on the device."""

        method = "global.getCurrentTime"
        r = self.request(method=method)

        if r['result'] is False:
            return None

        return r['params']['time']
    
    def get_logs(self, start_time, end_time, log_type="All", token=00000000):
        method = "log.doSeekFind"
        params = {
            # "condition": {
            #     "StartTime": start_time,  # Format: "YYYY-MM-DD HH:MM:SS"
            #     "EndTime": end_time,      # Format: "YYYY-MM-DD HH:MM:SS"
            #     "Type": log_type          # Options: "All", "System", "Alarm", etc.
            # },
            "offset": 0,  # Starting point for log retrieval
            "token": token,  # Token for the request, usually a constant value
            "count": 100  # Number of log entries to retrieve
        }
        return self.request(method, params)
